{"cells": [{"cell_type": "code", "execution_count": 24, "id": "fb004e76", "metadata": {}, "outputs": [], "source": ["!pip install -q langchain-google-genai langchain-core langchain pinecone python-dotenv langchain-community langchain-pinecone\n"]}, {"cell_type": "code", "execution_count": 25, "id": "13a6f22c", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()\n", "\n", "os.environ[\"GOOGLE_API_KEY\"] = os.getenv(\"GOOGLE_API_KEY\")\n", "PINECONE_API_KEY = os.getenv(\"PINECONE_API_KEY\")\n", "PINECONE_INDEX_NAME = os.getenv(\"PINECONE_INDEX_NAME\")\n"]}, {"cell_type": "code", "execution_count": 26, "id": "59161e41", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/media/riaz37/WebDevelopment/medical-bot/venv/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "Error loading file data/Medical_book.pdf\n"]}, {"ename": "RuntimeError", "evalue": "Error loading data/Medical_book.pdf", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mUnicodeDecodeError\u001b[39m                        Trace<PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m/media/riaz37/WebDevelopment/medical-bot/venv/lib/python3.12/site-packages/langchain_community/document_loaders/text.py:43\u001b[39m, in \u001b[36mTextLoader.lazy_load\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     42\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[38;5;28mself\u001b[39m.file_path, encoding=\u001b[38;5;28mself\u001b[39m.encoding) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m---> \u001b[39m\u001b[32m43\u001b[39m         text = \u001b[43mf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     44\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mUnicodeDecodeError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen codecs>:322\u001b[39m, in \u001b[36mdecode\u001b[39m\u001b[34m(self, input, final)\u001b[39m\n", "\u001b[31mUnicodeDecodeError\u001b[39m: 'utf-8' codec can't decode byte 0xe2 in position 10: invalid continuation byte", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[26]\u001b[39m\u001b[32m, line 13\u001b[39m\n\u001b[32m     11\u001b[39m \u001b[38;5;66;03m# Load and split documents\u001b[39;00m\n\u001b[32m     12\u001b[39m loader = DirectoryLoader(\u001b[33m'\u001b[39m\u001b[33m./data\u001b[39m\u001b[33m'\u001b[39m, loader_cls=TextLoader)\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m docs = \u001b[43mloader\u001b[49m\u001b[43m.\u001b[49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     15\u001b[39m splitter = RecursiveCharacterTextSplitter(chunk_size=\u001b[32m1000\u001b[39m, chunk_overlap=\u001b[32m100\u001b[39m)\n\u001b[32m     16\u001b[39m splits = splitter.split_documents(docs)\n", "\u001b[36mFile \u001b[39m\u001b[32m/media/riaz37/WebDevelopment/medical-bot/venv/lib/python3.12/site-packages/langchain_community/document_loaders/directory.py:117\u001b[39m, in \u001b[36mDirectoryLoader.load\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    115\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mload\u001b[39m(\u001b[38;5;28mself\u001b[39m) -> List[Document]:\n\u001b[32m    116\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Load documents.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m117\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mlazy_load\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/media/riaz37/WebDevelopment/medical-bot/venv/lib/python3.12/site-packages/langchain_community/document_loaders/directory.py:195\u001b[39m, in \u001b[36mDirectoryLoader.lazy_load\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    193\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    194\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m items:\n\u001b[32m--> \u001b[39m\u001b[32m195\u001b[39m         \u001b[38;5;28;01<PERSON>ield from\u001b[39;00m \u001b[38;5;28mself\u001b[39m._lazy_load_file(i, p, pbar)\n\u001b[32m    197\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m pbar:\n\u001b[32m    198\u001b[39m     pbar.close()\n", "\u001b[36mFile \u001b[39m\u001b[32m/media/riaz37/WebDevelopment/medical-bot/venv/lib/python3.12/site-packages/langchain_community/document_loaders/directory.py:233\u001b[39m, in \u001b[36mDirectoryLoader._lazy_load_file\u001b[39m\u001b[34m(self, item, path, pbar)\u001b[39m\n\u001b[32m    231\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    232\u001b[39m         logger.error(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError loading file \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(item)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m233\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m    234\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    235\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m pbar:\n", "\u001b[36mFile \u001b[39m\u001b[32m/media/riaz37/WebDevelopment/medical-bot/venv/lib/python3.12/site-packages/langchain_community/document_loaders/directory.py:223\u001b[39m, in \u001b[36mDirectoryLoader._lazy_load_file\u001b[39m\u001b[34m(self, item, path, pbar)\u001b[39m\n\u001b[32m    221\u001b[39m loader = \u001b[38;5;28mself\u001b[39m.loader_cls(\u001b[38;5;28mstr\u001b[39m(item), **\u001b[38;5;28mself\u001b[39m.loader_kwargs)\n\u001b[32m    222\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m223\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msubdoc\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mloader\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlazy_load\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    224\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43;01myield\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msubdoc\u001b[49m\n\u001b[32m    225\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m/media/riaz37/WebDevelopment/medical-bot/venv/lib/python3.12/site-packages/langchain_community/document_loaders/text.py:56\u001b[39m, in \u001b[36mTextLoader.lazy_load\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     54\u001b[39m                 \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m     55\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m56\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError loading \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.file_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n\u001b[32m     57\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m     58\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mError loading \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.file_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n", "\u001b[31mRuntimeError\u001b[39m: Error loading data/Medical_book.pdf"]}], "source": ["from pinecone import Pinecone\n", "from langchain.document_loaders import DirectoryLoader, TextLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "from langchain_pinecone import PineconeVectorStore  \n", "\n", "# Init Pinecone\n", "pc = Pinecone(api_key=PINECONE_API_KEY)\n", "index = pc.Index(PINECONE_INDEX_NAME)\n", "\n", "# Load and split documents\n", "loader = DirectoryLoader('./data', loader_cls=TextLoader)\n", "docs = loader.load()\n", "\n", "splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)\n", "splits = splitter.split_documents(docs)\n", "\n", "# Create embeddings\n", "embedding = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")\n", "\n", "# Upload vectors to Pinecone\n", "vectorstore = PineconeVectorStore.from_documents(  \n", "    documents=splits,\n", "    embedding=embedding,\n", "    index_name=PINECONE_INDEX_NAME  \n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0c0c52ce", "metadata": {}, "outputs": [], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "from langchain.chains import RetrievalQA\n", "\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-2.0-flash\", temperature=0.2)\n", "\n", "retriever = Pinecone.from_existing_index(\n", "    index_name=PINECONE_INDEX_NAME,\n", "    embedding=embedding\n", ").as_retriever()\n", "\n", "qa_chain = RetrievalQA.from_chain_type(\n", "    llm=llm,\n", "    retriever=retriever,\n", "    return_source_documents=True\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "1e66ec7b", "metadata": {}, "outputs": [], "source": ["query = \"What are the symptoms of atrial fibrillation?\"\n", "result = qa_chain.invoke(query)\n", "print(\"Answer:\", result[\"result\"])\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}